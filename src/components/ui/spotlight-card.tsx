import React, { ReactNode } from 'react';
import { cn } from "@/lib/utils";

interface SpotlightCardProps {
  children: ReactNode;
  className?: string;
  listColor?: string;
  enableSpotlight?: boolean;
  spotlightIntensity?: 'subtle' | 'medium' | 'strong';
  style?: React.CSSProperties;
}

const SpotlightCard: React.FC<SpotlightCardProps> = ({
  children,
  className = '',
  listColor,
  enableSpotlight = true,
  spotlightIntensity = 'subtle',
  style
}) => {
  const getSpotlightIntensity = () => {
    switch (spotlightIntensity) {
      case 'subtle': return { bgOpacity: 0.08, borderOpacity: 0.15 };
      case 'medium': return { bgOpacity: 0.12, borderOpacity: 0.25 };
      case 'strong': return { bgOpacity: 0.18, borderOpacity: 0.35 };
      default: return { bgOpacity: 0.08, borderOpacity: 0.15 };
    }
  };

  const { bgOpacity, borderOpacity } = getSpotlightIntensity();

  // Determine spotlight color based on list color or default theme
  const getSpotlightColor = () => {
    if (listColor) {
      return listColor;
    }
    return 'hsl(220, 70%, 60%)'; // Default blue
  };

  const spotlightColor = getSpotlightColor();

  // Debug logging (remove in production)
  if (process.env.NODE_ENV === 'development') {
    console.log('Spotlight Debug:', {
      listColor,
      spotlightColor,
      bgOpacity,
      borderOpacity,
      enableSpotlight,
      spotlightIntensity
    });
  }

  // Create static gradient background that mimics the animated spotlight
  const getStaticSpotlightBackground = () => {
    if (!enableSpotlight) return {};

    // Static positions that match the average positions from the animation
    const primaryX = 50; // Center horizontally
    const primaryY = 25; // Upper portion anchor
    const secondaryX = 50; // Center horizontally
    const secondaryY = 75; // Lower portion anchor

    return {
      background: `
        radial-gradient(1000px circle at ${primaryX}% ${primaryY}%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(bgOpacity * 100)}%, transparent) 0%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(bgOpacity * 40)}%, transparent) 50%,
          transparent 90%),
        radial-gradient(800px circle at ${secondaryX}% ${secondaryY}%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(bgOpacity * 60)}%, transparent) 0%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(bgOpacity * 25)}%, transparent) 50%,
          transparent 90%)
      `
    };
  };

  // Create static border gradient
  const getStaticBorderBackground = () => {
    if (!enableSpotlight) return {};

    const primaryX = 50;
    const primaryY = 25;
    const secondaryX = 50;
    const secondaryY = 75;

    return {
      background: `
        radial-gradient(700px circle at ${primaryX}% ${primaryY}%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(borderOpacity * 100)}%, transparent) 0%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(borderOpacity * 50)}%, transparent) 40%,
          transparent 85%),
        radial-gradient(600px circle at ${secondaryX}% ${secondaryY}%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(borderOpacity * 80)}%, transparent) 0%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(borderOpacity * 30)}%, transparent) 40%,
          transparent 85%)
      `
    };
  };

  return (
    <div
      data-slot="card"
      data-intensity={spotlightIntensity}
      className={cn(
        "bg-card text-card-foreground flex flex-col gap-4 rounded-xl border py-4 shadow-sm relative overflow-hidden",
        className
      )}
      style={{
        '--spotlight-color': spotlightColor,
        '--spotlight-bg-opacity': bgOpacity,
        '--spotlight-border-opacity': borderOpacity,
        ...style,
      } as React.CSSProperties}
    >
      {enableSpotlight && (
        <>
          {/* Static background spotlight effect */}
          <div
            className="absolute inset-0 pointer-events-none"
            style={getStaticSpotlightBackground()}
          />
          {/* Static border spotlight effect */}
          <div
            className="absolute inset-0 pointer-events-none rounded-xl"
            style={{
              ...getStaticBorderBackground(),
              mask: 'linear-gradient(white, white) content-box, linear-gradient(white, white)',
              maskComposite: 'xor',
              WebkitMask: 'linear-gradient(white, white) content-box, linear-gradient(white, white)',
              WebkitMaskComposite: 'xor',
              padding: '1px',
            }}
          />
        </>
      )}
      {children}
    </div>
  );
};

export { SpotlightCard };